"use client";
import Image from "next/image";
import Link from "next/link";
import { useState } from "react";
import CleaningServiceRequest from "@/components/CleaningServiceRequest";

const locations = [
  {
    name: "Anchorage",
    slug: "anchorage",
    description: "Professional cleaning services for Anchorage, Alaska",
    image: "/office cleaning.png",
    population: "~290,000",
    features: ["Major City", "Commercial Hub", "High-Rise Buildings"]
  },
  {
    name: "Metlakatla",
    slug: "metlakatla",
    description: "Professional cleaning services for Metlakatla, Alaska",
    image: "/peak services employee putting on hardhat.png",
    population: "~1,400",
    features: ["Island Community", "Residential Services", "Commercial Cleaning"]
  },
  {
    name: "Wrangell",
    slug: "wrangell",
    description: "Reliable cleaning services for Wrangell, Alaska",
    image: "/residential bedroom cleaning.png",
    population: "~2,100",
    features: ["Historic Town", "Full Service Area", "Vacation Rentals"]
  },
  {
    name: "Gravina Island",
    slug: "gravina",
    description: "Island cleaning services for Gravina Island, Alaska",
    image: "/commercial floor cleaning.png",
    population: "~50",
    features: ["Remote Service", "Special Arrangements", "Island Access"]
  },
  {
    name: "Pennock Island",
    slug: "pennock",
    description: "Specialized cleaning for Pennock Island, Alaska",
    image: "/residential couch cleaning.png",
    population: "~100",
    features: ["Private Island", "Residential Focus", "Boat Access"]
  },
  {
    name: "Prince of Wales Island",
    slug: "prince-of-wales",
    description: "Comprehensive cleaning services for Prince of Wales Island, Alaska",
    image: "/commercial bathroom janitorial.png",
    population: "~5,500",
    features: ["Largest Island", "Multiple Communities", "Extended Service"]
  }
];

export default function LocationsPage() {
  const [showBookingModal, setShowBookingModal] = useState(false);

  return (
    <main className="flex flex-col items-center min-h-screen space-y-8 font-sans pt-20">
      {/* Service Request Modal */}
      {showBookingModal && (
        <CleaningServiceRequest onClose={() => setShowBookingModal(false)} />
      )}

      {/* Hero Section */}
      <section className="w-full py-16 px-4">
        <div className="max-w-6xl mx-auto text-center">
          <h1 className="text-4xl md:text-6xl font-bold text-white mb-6 tracking-tighter">
            Service Locations
          </h1>
          <p className="text-xl text-white/90 mb-8 max-w-3xl mx-auto">
            Peak Services AK proudly serves communities throughout Southeast Alaska with professional cleaning services
          </p>
          <button
            onClick={() => setShowBookingModal(true)}
            className="group relative text-black px-8 py-4 rounded-lg font-semibold transition-all transform hover:scale-105 hover:translate-y-[-2px] hover:shadow-lg border border-white"
            style={{ backgroundColor: '#dfff69' }}
          >
            <span className="relative z-10 italic font-bold text-xl tracking-tighter">
              REQUEST SERVICE IN YOUR AREA
            </span>
          </button>
        </div>
      </section>

      {/* Locations Grid */}
      <section className="w-full max-w-7xl mx-auto px-4 mb-16">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {locations.map((location) => (
            <Link
              key={location.slug}
              href={`/locations/${location.slug}`}
              className="group block"
            >
              <div className="bg-[rgba(15,55,255,0.9)] backdrop-blur-sm rounded-xl overflow-hidden border border-white/20 transition-all duration-300 hover:scale-105 hover:shadow-xl">
                <div className="relative h-48 overflow-hidden">
                  <Image
                    src={location.image}
                    alt={`${location.name} cleaning services`}
                    fill
                    className="object-cover transition-transform duration-300 group-hover:scale-110"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent"></div>
                  <div className="absolute bottom-4 left-4 text-white">
                    <h3 className="text-2xl font-bold tracking-tighter">{location.name}</h3>
                    <p className="text-white/80">Population: {location.population}</p>
                  </div>
                </div>
                
                <div className="p-6">
                  <p className="text-white/90 mb-4">{location.description}</p>
                  
                  <div className="space-y-2">
                    {location.features.map((feature, index) => (
                      <div key={index} className="flex items-center text-white/80">
                        <span className="text-green-400 mr-2">✓</span>
                        <span className="text-sm">{feature}</span>
                      </div>
                    ))}
                  </div>
                  
                  <div className="mt-4 text-center">
                    <span className="inline-block px-4 py-2 bg-white/10 rounded-lg text-white font-medium group-hover:bg-white/20 transition-colors">
                      View Details →
                    </span>
                  </div>
                </div>
              </div>
            </Link>
          ))}
        </div>
      </section>

      {/* Service Area Info */}
      <section className="w-full max-w-4xl mx-auto px-4 mb-16">
        <div className="bg-[rgba(15,55,255,0.9)] backdrop-blur-sm rounded-xl p-8 border border-white/20">
          <h2 className="text-3xl font-bold text-white mb-6 text-center tracking-tighter">
            Southeast Alaska Coverage
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div>
              <h3 className="text-xl font-bold text-white mb-4">Our Service Promise</h3>
              <ul className="space-y-2 text-white/90">
                <li className="flex items-center">
                  <span className="text-green-400 mr-3">✓</span>
                  Licensed, Bonded & Insured
                </li>
                <li className="flex items-center">
                  <span className="text-green-400 mr-3">✓</span>
                  Professional Team of 5 Cleaners
                </li>
                <li className="flex items-center">
                  <span className="text-green-400 mr-3">✓</span>
                  24-Hour Happiness Guarantee
                </li>
                <li className="flex items-center">
                  <span className="text-green-400 mr-3">✓</span>
                  Eco-Friendly Options Available
                </li>
              </ul>
            </div>
            
            <div>
              <h3 className="text-xl font-bold text-white mb-4">Contact Information</h3>
              <div className="space-y-2 text-white/90">
                <p><strong>Phone:</strong> (*************</p>
                <p><strong>Email:</strong> <EMAIL></p>
                <p><strong>License:</strong> Alaska Business License #2164310</p>
                <p><strong>Response Time:</strong> Within 24 hours</p>
              </div>
            </div>
          </div>
        </div>
      </section>
    </main>
  );
}
