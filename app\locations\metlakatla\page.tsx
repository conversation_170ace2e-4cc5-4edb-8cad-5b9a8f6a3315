"use client";
import Image from "next/image";
import { useState } from "react";
import CleaningServiceRequest from "@/components/CleaningServiceRequest";

const cleaningServices = [
  {
    name: "Residential Cleaning",
    image: "/residential bedroom cleaning.png",
    icon: "🏠",
    description: "Complete home cleaning services for Metlakatla residents"
  },
  {
    name: "Commercial Cleaning",
    image: "/commercial floor cleaning.png", 
    icon: "🏢",
    description: "Professional office and business cleaning in Metlakatla"
  },
  {
    name: "Deep Cleaning",
    image: "/Bathroom floor cleaning.png",
    icon: "✨",
    description: "Thorough deep cleaning services for Metlakatla homes and businesses"
  },
  {
    name: "Vacation Rental Cleaning",
    image: "/residential couch cleaning.png",
    icon: "🏖️", 
    description: "Turnover cleaning for Metlakatla vacation rentals"
  },
  {
    name: "Carpet Shampooing",
    image: "/carpet cleaning.png",
    icon: "🧽",
    description: "Professional carpet cleaning services in Metlakatla"
  },
  {
    name: "Window Washing",
    image: "/peak services window cleaning.png",
    icon: "🪟",
    description: "Crystal clear window cleaning for Metlakatla properties"
  }
];

export default function MetlakatlaPage() {
  const [showBookingModal, setShowBookingModal] = useState(false);

  return (
    <main className="flex flex-col items-center min-h-screen space-y-8 font-sans pt-20">
      {/* Service Request Modal */}
      {showBookingModal && (
        <CleaningServiceRequest onClose={() => setShowBookingModal(false)} />
      )}

      {/* Hero Section */}
      <section className="w-full py-16 px-4">
        <div className="max-w-6xl mx-auto text-center">
          <h2 className="text-2xl md:text-3xl font-thin text-white tracking-tighter mb-2">
            Consistent • Reliable • Trusted
          </h2>
          <h1 className="text-4xl md:text-6xl font-bold text-white mb-6 tracking-tighter">
            Metlakatla Cleaning Services
          </h1>
          <p className="text-xl text-white/90 mb-8 max-w-3xl mx-auto">
            Professional cleaning services for Metlakatla, Alaska. Our team provides reliable residential and commercial cleaning throughout this beautiful island community.
          </p>
          
          <div className="flex justify-center px-8 lg:px-0">
            <div className="relative">
              <span className="absolute -left-10 top-1/2 -translate-y-1/2 text-4xl z-20">
                <span className="inline-block wave-animation">👉</span>
              </span>
              <button
                onClick={() => setShowBookingModal(true)}
                className="group relative text-black px-6 py-3 rounded-lg font-semibold transition-all transform hover:scale-105 hover:translate-y-[-5px] hover:shadow-lg shiny-button border border-white"
                style={{ backgroundColor: '#dfff69' }}
              >
                <span className="relative z-10 italic font-bold text-xl tracking-tighter">
                  REQUEST METLAKATLA CLEANING SERVICE
                </span>
              </button>
            </div>
          </div>
        </div>
      </section>

      {/* Services Section */}
      <section className="py-7 px-12 mb-8 border border-white rounded-xl w-fit mx-auto bg-white/50 backdrop-blur-xl">
        <div className="flex justify-start mb-6">
          <h2 className="font-bold text-3xl md:text-5xl tracking-tighter" style={{ color: '#0040a0ff' }}>
            Our Metlakatla Services:
          </h2>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-2 justify-items-center">
          {cleaningServices.map((service, idx) => (
            <div
              key={service.name}
              className="group overflow-hidden relative rounded-xl w-80 h-54"
              style={{ backgroundColor: "#dfff69" }}
            >
              <div className="relative h-40 overflow-hidden rounded-t-xl">
                <Image
                  src={service.image}
                  alt={`${service.name} in Metlakatla`}
                  fill
                  className="object-cover transition-transform duration-300 group-hover:scale-110"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
                <div className="absolute top-2 right-2 text-2xl bg-white/20 backdrop-blur-sm rounded-full p-2">
                  {service.icon}
                </div>
              </div>
              <div className="p-4 text-center">
                <h3 className="text-lg font-semibold text-black text-center tracking-tighter">
                  {service.name}
                </h3>
              </div>
            </div>
          ))}
        </div>
      </section>

      {/* Local Info Section */}
      <section className="w-full max-w-4xl mx-auto px-4 mb-16">
        <div className="bg-[rgba(15,55,255,0.9)] backdrop-blur-sm rounded-xl p-8 border border-white/20">
          <h2 className="text-3xl font-bold text-white mb-6 text-center tracking-tighter">
            Serving Metlakatla, Alaska
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div>
              <h3 className="text-xl font-bold text-white mb-4">About Metlakatla</h3>
              <ul className="space-y-2 text-white/90">
                <li className="flex items-center">
                  <span className="text-green-400 mr-3">📍</span>
                  Only reservation in Alaska
                </li>
                <li className="flex items-center">
                  <span className="text-green-400 mr-3">🏝️</span>
                  Located on Annette Island
                </li>
                <li className="flex items-center">
                  <span className="text-green-400 mr-3">👥</span>
                  Population: ~1,400 residents
                </li>
                <li className="flex items-center">
                  <span className="text-green-400 mr-3">🎣</span>
                  Rich fishing community
                </li>
              </ul>
            </div>
            
            <div>
              <h3 className="text-xl font-bold text-white mb-4">Our Metlakatla Promise</h3>
              <ul className="space-y-2 text-white/90">
                <li className="flex items-center">
                  <span className="text-green-400 mr-3">✓</span>
                  Island community specialists
                </li>
                <li className="flex items-center">
                  <span className="text-green-400 mr-3">✓</span>
                  Flexible scheduling
                </li>
                <li className="flex items-center">
                  <span className="text-green-400 mr-3">✓</span>
                  Cultural sensitivity
                </li>
                <li className="flex items-center">
                  <span className="text-green-400 mr-3">✓</span>
                  24-hour response guarantee
                </li>
              </ul>
            </div>
          </div>
          
          <div className="mt-8 text-center">
            <div className="bg-green-500/20 rounded-lg border border-green-400/30 p-4">
              <p className="text-green-300">
                <strong>✅ Licensed, Bonded & Insured</strong><br />
                Alaska Business License #: 2164310<br />
                Phone: (************* | Email: <EMAIL>
              </p>
            </div>
          </div>
        </div>
      </section>
    </main>
  );
}
