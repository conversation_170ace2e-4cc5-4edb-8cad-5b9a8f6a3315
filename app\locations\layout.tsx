import { Metadata } from 'next';

export const metadata: Metadata = {
    title: 'Service Locations | Peak Services AK - Southeast Alaska Cleaning',
    description: 'Peak Services AK provides professional cleaning services throughout Southeast Alaska including Metlakatla, Wrangell, Gravina Island, Pennock Island, and Prince of Wales Island.',
    keywords: [
        'Alaska cleaning services',
        'Southeast Alaska cleaning',
        'Metlakatla cleaning',
        'Wrangell cleaning',
        'Gravina Island cleaning',
        'Pennock Island cleaning',
        'Prince of Wales Island cleaning',
        'Ketchikan area cleaning',
        'island cleaning services',
        'remote Alaska cleaning'
    ],
    alternates: {
        canonical: 'https://peakservicesak.com/locations/',
    },
    openGraph: {
        title: 'Service Locations | Peak Services AK - Southeast Alaska Cleaning',
        description: 'Peak Services AK provides professional cleaning services throughout Southeast Alaska including Metlakatla, Wrangell, Gravina Island, Pennock Island, and Prince of Wales Island.',
        url: 'https://peakservicesak.com/locations/',
        siteName: 'Peak Services AK',
        images: [
            {
                url: '/peak services employee putting on hardhat.png',
                width: 1200,
                height: 630,
                alt: 'Peak Services AK cleaning team serving Southeast Alaska',
            },
        ],
        locale: 'en_US',
        type: 'website',
    },
    twitter: {
        card: 'summary_large_image',
        title: 'Service Locations | Peak Services AK - Southeast Alaska Cleaning',
        description: 'Peak Services AK provides professional cleaning services throughout Southeast Alaska including Metlakatla, Wrangell, Gravina Island, Pennock Island, and Prince of Wales Island.',
        images: ['/peak services employee putting on hardhat.png'],
    }
};

export default function LocationsLayout({
    children,
}: {
    children: React.ReactNode;
}) {
    return (
        <>
            {/* Structured Data for SEO */}
            <script
                type="application/ld+json"
                dangerouslySetInnerHTML={{
                    __html: JSON.stringify({
                        "@context": "https://schema.org",
                        "@type": "Service",
                        "name": "Peak Services AK Cleaning Services",
                        "description": "Professional cleaning services throughout Southeast Alaska",
                        "provider": {
                            "@type": "LocalBusiness",
                            "name": "Peak Services AK",
                            "url": "https://peakservicesak.com/",
                            "telephone": "******-821-1335",
                            "email": "<EMAIL>",
                            "address": {
                                "@type": "PostalAddress",
                                "addressRegion": "AK",
                                "addressCountry": "US"
                            }
                        },
                        "areaServed": [
                            {
                                "@type": "City",
                                "name": "Metlakatla",
                                "addressRegion": "AK",
                                "addressCountry": "US"
                            },
                            {
                                "@type": "City", 
                                "name": "Wrangell",
                                "addressRegion": "AK",
                                "addressCountry": "US"
                            },
                            {
                                "@type": "Place",
                                "name": "Gravina Island",
                                "addressRegion": "AK",
                                "addressCountry": "US"
                            },
                            {
                                "@type": "Place",
                                "name": "Pennock Island", 
                                "addressRegion": "AK",
                                "addressCountry": "US"
                            },
                            {
                                "@type": "Place",
                                "name": "Prince of Wales Island",
                                "addressRegion": "AK", 
                                "addressCountry": "US"
                            }
                        ]
                    })
                }}
            />
            {children}
        </>
    );
}
